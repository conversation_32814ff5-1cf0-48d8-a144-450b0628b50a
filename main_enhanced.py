# coding:utf-8
# main_enhanced.py
"""
微信公众号爬虫工具集 v2.3
仅保留：抓取Cookie和批量内容+统计抓取功能（推荐）
"""

import os
from read_cookie import ReadCookie
from batch_readnum_spider import BatchReadnumSpider

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🚀 微信公众号爬虫工具集")
    print("="*60)
    print("1. 抓取Cookie（首次使用或Cookie过期时）")
    print("2. 批量内容+统计抓取（推荐：内容+阅读量+点赞数+分享数）")
    print("3. 退出程序")
    print("="*60)

def extract_cookies():
    """抓取Cookie"""
    print("\n🔧 启动Cookie抓取器...")
    cookie_reader = ReadCookie()
    
    print("请选择操作:")
    print("1. 自动启动抓取器（需要手动访问微信公众号）")
    print("2. 只解析现有cookie文件")
    
    choice = input("请选择(1/2): ").strip()
    
    if choice == '1':
        # 启动抓取器
        if cookie_reader.start_cookie_extractor(timeout=120):
            print("\n抓取完成，开始解析...")
        else:
            print("抓取器启动失败")
            return False
    
    # 解析cookie
    result = cookie_reader.get_latest_cookies()
    
    if result:
        print("\n" + "="*50)
        print("✅ Cookie解析成功:")
        print(f"   __biz: {result['biz']}")
        print(f"   appmsg_token: {result['appmsg_token'][:20]}...")
        print(f"   解析时间: {result['timestamp']}")
        print("="*50)
        return True
    else:
        print("❌ Cookie解析失败，请确保:")
        print("1. 已正确访问微信公众号文章")
        print("2. 代理设置正确(127.0.0.1:8080)")
        print("3. wechat_keys.txt文件中有有效数据")
        return False

def batch_readnum_crawler():
    """批量文章内容+统计数据抓取"""
    print("\n📊 启动批量内容+统计抓取器（文章内容+阅读量+点赞数+分享数）...")
    
    # 检查cookie文件
    if not os.path.exists('wechat_keys.txt'):
        print("❌ 未找到cookie文件，请先抓取Cookie")
        return
    
    # 获取用户配置
    print("\n请配置抓取参数:")
    try:
        max_pages = int(input("最大页数 (默认3): ") or "3")
        articles_per_page = int(input("每页文章数 (默认10): ") or "10")
        days_back = int(input("抓取多少天内的文章 (默认7): ") or "7")
    except ValueError:
        print("❌ 参数输入无效，使用默认值")
        max_pages, articles_per_page, days_back = 3, 10, 7
    
    # 初始化爬虫
    spider = BatchReadnumSpider()
    
    try:
        print(f"\n🚀 开始批量抓取...")
        print(f"📋 配置: {max_pages}页 × {articles_per_page}篇/页，{days_back}天内文章")
        
        # 执行抓取
        results = spider.batch_crawl_radnum(
            max_pages=max_pages,
            articles_per_page=articles_per_page,
            days_back=days_back
        )
        
        if results:
            # 显示统计摘要
            spider.print_summary()
            
            # 保存数据
            print(f"\n💾 正在保存数据...")
            excel_file = spider.save_to_excel()
            json_file = spider.save_to_json()
            
            print(f"\n🎉 抓取完成！")
            if excel_file:
                print(f"📊 Excel文件: {excel_file}")
            if json_file:
                print(f"💾 JSON文件: {json_file}")
        else:
            print("❌ 未获取到任何数据，请检查Cookie是否有效")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断抓取")
        if spider.articles_data:
            print("💾 保存已抓取的数据...")
            spider.save_to_excel()
            spider.save_to_json()
    except Exception as e:
        print(f"❌ 抓取过程出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主程序入口"""
    print("🎯 微信公众号爬虫工具集 v2.2")
    while True:
        show_menu()
        choice = input("\n请选择功能 (1-3): ").strip()
        if choice == '1':
            extract_cookies()
        elif choice == '2':
            batch_readnum_crawler()
        elif choice == '3':
            print("👋 感谢使用，再见！")
            break
        else:
            print("❌ 无效选择，请输入1-3")

if __name__ == '__main__':
    main()
