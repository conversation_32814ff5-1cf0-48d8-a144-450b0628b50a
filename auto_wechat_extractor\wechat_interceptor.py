import mitmproxy.http
from mitmproxy import ctx
from datetime import datetime
import os

class WeChatExtractor:
    def __init__(self):
        self.output_file = "wechat_data.txt"
        self.headers_to_log = [
            "x-wechat-key",
            "x-wechat-uin",
            "exportkey",
            "user-agent",
            "accept",
            "accept-language",
            "cache-control",
            "sec-fetch-site",
            "sec-fetch-mode",
            "sec-fetch-dest",
            "priority",
        ]
        self.start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self._initialize_file()

    def _initialize_file(self):
        """Initializes the output file with a header if it doesn't exist."""
        if not os.path.exists(self.output_file):
            with open(self.output_file, "w", encoding="utf-8") as f:
                f.write("=== 微信公众号Keys和URLs记录 ===\n")
                f.write(f"开始时间: {self.start_time}\n\n")

    def request(self, flow: mitmproxy.http.HTTPFlow):
        """
        The full HTTP request has been read.
        """
        # Check if the request is for a WeChat article
        if "mp.weixin.qq.com/s" in flow.request.pretty_url:
            ctx.log.info(f"WeChat Article Detected: {flow.request.pretty_url}")
            
            # Prepare the data to be logged
            log_entry = []
            log_entry.append("============================================================")
            log_entry.append(f"time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            log_entry.append(f"allurl: {flow.request.pretty_url}")
            
            # Extract Cookies
            cookies = flow.request.headers.get("Cookie", "N/A")
            log_entry.append(f"Cookies: {cookies}")
            
            # Extract specific headers
            log_entry.append("Headers:")
            for header in self.headers_to_log:
                header_value = flow.request.headers.get(header, "N/A")
                log_entry.append(f"  {header}: {header_value}")
            
            log_entry.append("") # Add a blank line for separation

            # Write the entry to the output file
            with open(self.output_file, "a", encoding="utf-8") as f:
                f.write("\n".join(log_entry))
            
            ctx.log.info(f"Successfully logged data to {self.output_file}")

addons = [
    WeChatExtractor()
]